const request = require('supertest');
const express = require('express');
const userRoutes = require('../../routes/userRoutes');

// Mock User model methods for integration tests
const User = require('../../models/User');
jest.mock('../../models/User', () => {
  const originalModule = jest.requireActual('../../models/User');
  return {
    ...originalModule,
    findByCredentials: jest.fn(),
  };
});

describe('User Routes Integration Tests', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use('/api/users', userRoutes);
    
    // Simple error handler for tests
    app.use((err, req, res, next) => {
      console.error('Test error:', err.message);
      res.status(err.statusCode || 500).json({
        status: 'error',
        message: err.message
      });
    });

    // Reset User model mocks
    jest.clearAllMocks();
  });

  describe('POST /api/users/register', () => {
    const validUserData = {
      username: 'newuser',
      email: '<EMAIL>',
      password: 'Password123',
      firstName: 'New',
      lastName: 'User',
    };

    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/api/users/register')
        .send(validUserData)
        .expect(201);

      expect(response.body).toMatchObject({
        status: 'success',
        message: 'User registered successfully',
        data: {
          user: expect.objectContaining({
            username: 'newuser',
            email: '<EMAIL>',
          }),
        },
      });
    });

    it('should return 400 for invalid user data', async () => {
      const invalidUserData = {
        username: 'ab', // Too short
        email: 'invalid-email',
        password: '123', // Too short
      };

      const response = await request(app)
        .post('/api/users/register')
        .send(invalidUserData)
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Username must be at least 3 characters');
    });

    it('should return 400 if user already exists', async () => {
      // First, create a user
      await request(app)
        .post('/api/users/register')
        .send(validUserData)
        .expect(201);

      // Then try to register the same user data again
      const response = await request(app)
        .post('/api/users/register')
        .send(validUserData)
        .expect(400);

      expect(response.body.status).toBe('error');
    });

    it('should handle missing required fields', async () => {
      const incompleteData = {
        username: 'testuser',
        // Missing email and password
      };

      const response = await request(app)
        .post('/api/users/register')
        .send(incompleteData)
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Email is required');
    });
  });

  describe('POST /api/users/login', () => {
    const validCredentials = {
      email: '<EMAIL>',
      password: 'Password123',
    };

    it('should login user successfully', async () => {
      // Mock findByCredentials to return the seeded user
      User.findByCredentials.mockResolvedValue({
        id: 'user123',
        username: 'testuser',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'user',
        isActive: true,
        correctPassword: jest.fn().mockResolvedValue(true),
      });

      const response = await request(app)
        .post('/api/users/login')
        .send(validCredentials)
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        message: 'Login successful',
        data: {
          user: expect.objectContaining({
            email: '<EMAIL>',
          }),
        },
      });
    });

    it('should return 401 for invalid credentials', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })
        .expect(401);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Invalid credentials');
    });

    it('should return 400 for missing credentials', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          email: '<EMAIL>',
          // Missing password
        })
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Please provide email and password');
    });

    it('should handle invalid email format', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          email: 'invalid-email',
          password: 'password123',
        })
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('GET /api/users/profile', () => {
    it('should get user profile successfully', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('x-user-id', 'user123')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            username: 'testuser',
            email: '<EMAIL>',
          }),
        },
      });
    });

    it('should return 401 when user not authenticated', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .expect(401);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('User not authenticated');
    });

    it('should return 404 when user not found', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('x-user-id', 'nonexistent')
        .expect(404);

      expect(response.body.status).toBe('error');
    });
  });

  describe('PATCH /api/users/profile', () => {
    const updateData = {
      firstName: 'Updated',
      lastName: 'Name',
      profile: {
        bio: 'Updated bio',
      },
    };

    it('should update user profile successfully', async () => {
      const response = await request(app)
        .patch('/api/users/profile')
        .set('x-user-id', 'user123')
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        message: 'Profile updated successfully',
        data: {
          user: expect.objectContaining({
            firstName: 'Updated',
            lastName: 'Name',
          }),
        },
      });
    });

    it('should reject password updates through profile route', async () => {
      const response = await request(app)
        .patch('/api/users/profile')
        .set('x-user-id', 'user123')
        .send({
          firstName: 'Updated',
          password: 'newpassword',
        })
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('not for password updates');
    });

    it('should validate update data', async () => {
      const response = await request(app)
        .patch('/api/users/profile')
        .set('x-user-id', 'user123')
        .send({
          email: 'invalid-email',
        })
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('PATCH /api/users/change-password', () => {
    const passwordData = {
      currentPassword: 'Password123',
      newPassword: 'NewPassword123',
    };

    it('should change password successfully', async () => {
      const response = await request(app)
        .patch('/api/users/change-password')
        .set('x-user-id', 'user123')
        .send(passwordData)
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        message: 'Password changed successfully',
      });
    });

    it('should return 400 for missing password data', async () => {
      const response = await request(app)
        .patch('/api/users/change-password')
        .set('x-user-id', 'user123')
        .send({
          currentPassword: 'Password123',
          // Missing newPassword
        })
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Please provide current and new password');
    });

    it('should return 400 for incorrect current password', async () => {
      const response = await request(app)
        .patch('/api/users/change-password')
        .set('x-user-id', 'user123')
        .send({
          currentPassword: 'WrongPassword',
          newPassword: 'NewPassword123',
        })
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('DELETE /api/users/profile', () => {
    it('should delete user profile successfully', async () => {
      const response = await request(app)
        .delete('/api/users/profile')
        .set('x-user-id', 'user123')
        .expect(204);
    });

    it('should return 401 when user not authenticated', async () => {
      const response = await request(app)
        .delete('/api/users/profile')
        .expect(401);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('User not authenticated');
    });
  });

  describe('GET /api/users (Admin)', () => {
    it('should get all users with pagination', async () => {
      const response = await request(app)
        .get('/api/users')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        results: 1,
        pagination: expect.objectContaining({
          limit: 10,
          page: 1,
        }),
        data: {
          users: expect.arrayContaining([
            expect.objectContaining({
              username: 'testuser',
              email: '<EMAIL>',
            }),
          ]),
        },
      });
    });

    it('should filter users by role', async () => {
      const response = await request(app)
        .get('/api/users')
        .query({ role: 'admin' })
        .expect(200);
    });

    it('should filter users by active status', async () => {
      const response = await request(app)
        .get('/api/users')
        .query({ isActive: 'true' })
        .expect(200);
    });
  });

  describe('GET /api/users/stats (Admin)', () => {
    it('should get user statistics', async () => {
      const response = await request(app)
        .get('/api/users/stats')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          stats: expect.objectContaining({
            totalUsers: expect.any(Number),
            activeUsers: expect.any(Number),
            verifiedUsers: expect.any(Number),
            adminUsers: expect.any(Number),
          }),
        },
      });
    });
  });
});
